# ninja log v5
12	184	7762769661760549	project_elf_src_esp32s3.c	519c17552de1e81
12	184	7762769661760549	E:/APPprj/ESP32-S3-Touch-LCD-1.85C-Test/build/bootloader/project_elf_src_esp32s3.c	519c17552de1e81
20	198	7762769662400686	esp-idf/xtensa/CMakeFiles/__idf_xtensa.dir/eri.c.obj	cc5f87fda86aa102
45	227	7762769662832073	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/interrupts.c.obj	7a6ed73864061b96
39	241	7762769663032014	esp-idf/soc/CMakeFiles/__idf_soc.dir/dport_access_common.c.obj	6f5244267c57627d
32	254	7762769663032014	esp-idf/soc/CMakeFiles/__idf_soc.dir/lldesc.c.obj	540d88ea01b0648b
26	266	7762769663188064	esp-idf/xtensa/CMakeFiles/__idf_xtensa.dir/xt_trax.c.obj	4cfb5895dc52a8ff
69	296	7762769663513639	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/adc_periph.c.obj	a2d7a402bd1b1442
52	308	7762769663513639	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/gpio_periph.c.obj	25aa08b3623bd380
60	322	7762769663543679	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/uart_periph.c.obj	f5de4913be5ee56c
88	332	7762769663973661	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/gdma_periph.c.obj	403a4cb43f97efd7
77	348	7762769664093669	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/dedic_gpio_periph.c.obj	2cbc8ca377aa7328
121	359	7762769664153747	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/pcnt_periph.c.obj	e8d7eab9a373976e
111	372	7762769664223709	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/ledc_periph.c.obj	b3c310d61a75ad88
133	385	7762769664223709	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/rmt_periph.c.obj	bbd52a710476a59
145	401	7762769664243759	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/sdm_periph.c.obj	529633dbdaaf28f1
100	412	7762769664499018	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/spi_periph.c.obj	df042419482aa4e0
186	425	7762769664709025	CMakeFiles/bootloader.elf.dir/project_elf_src_esp32s3.c.obj	a58b28f8cbb1fea9
200	437	7762769664865482	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/temperature_sensor_periph.c.obj	fa8748bece3b4ccd
172	449	7762769664895491	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/i2c_periph.c.obj	76f0f46d3f4c62c2
159	461	7762769665015467	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/i2s_periph.c.obj	1812717c9c38301b
241	481	7762769665443105	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/lcd_periph.c.obj	31bc9883a4eb4daf
227	513	7762769665756190	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/timer_periph.c.obj	1003a5a774cb9ff
254	525	7762769665816070	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/mcpwm_periph.c.obj	47dbad693297fe4a
308	536	7762769665916166	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/touch_sensor_periph.c.obj	da74350ea9e6ea43
267	549	7762769666076851	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/mpi_periph.c.obj	de12cdc3abd45293
296	563	7762769666287003	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/sdmmc_periph.c.obj	4581fb0a68e0126c
322	576	7762769666376875	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/twai_periph.c.obj	8c5795d4232b9d33
348	593	7762769666526933	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/usb_dwc_periph.c.obj	9df89fee33ff8412
332	604	7762769666652266	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/usb_periph.c.obj	435cca33942ed8d3
360	646	7762769667067829	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/rtc_io_periph.c.obj	630c51727e36fa5c
401	734	7762769667817935	esp-idf/hal/CMakeFiles/__idf_hal.dir/mpu_hal.c.obj	e25007ab0c16d441
385	770	7762769668217784	esp-idf/hal/CMakeFiles/__idf_hal.dir/hal_utils.c.obj	4ac783543ea18004
481	798	7762769668467754	esp-idf/esp_bootloader_format/CMakeFiles/__idf_esp_bootloader_format.dir/esp_bootloader_desc.c.obj	9c38618b6a708917
413	849	7762769668827846	esp-idf/hal/CMakeFiles/__idf_hal.dir/efuse_hal.c.obj	ed4dbf2d9761183
425	868	7762769669157791	esp-idf/hal/CMakeFiles/__idf_hal.dir/esp32s3/efuse_hal.c.obj	16ec67dc5751148
437	938	7762769669967796	esp-idf/hal/CMakeFiles/__idf_hal.dir/mmu_hal.c.obj	86285570a010b1c2
564	951	7762769670077759	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_random.c.obj	4ba4a81047c77f9c
549	963	7762769670037758	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_mem.c.obj	7ba4c45a946aec7
461	975	7762769670127856	esp-idf/spi_flash/CMakeFiles/__idf_spi_flash.dir/spi_flash_wrap.c.obj	ede8417adab8411
449	989	7762769670247773	esp-idf/hal/CMakeFiles/__idf_hal.dir/cache_hal.c.obj	b6fd7eff4bf1e3c3
604	1002	7762769670347826	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/secure_boot.c.obj	c11862214310c5ee
576	1016	7762769670387901	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_efuse.c.obj	7b093e8bf96650f1
537	1031	7762769670437847	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_clock_init.c.obj	75b6cced939bac72
646	1045	7762769670817750	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_random_esp32s3.c.obj	ff491cfd526d30d4
525	1110	7762769671708383	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_common_loader.c.obj	44086ec41a09cf93
513	1123	7762769671798313	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_common.c.obj	e734be5d51669579
593	1136	7762769671808421	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/flash_encrypt.c.obj	5f7753f9fc226424
869	1255	7762769673128392	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/flash_partitions.c.obj	4022ac7175f17efe
770	1297	7762769673468314	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/bootloader_flash/src/flash_qio_mode.c.obj	e1902c90514d6d01
963	1308	7762769673568302	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_clock_loader.c.obj	bef93392c564e0b1
989	1344	7762769674048351	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_console_loader.c.obj	916e2880608f81d0
799	1356	7762769674078305	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/bootloader_flash/src/bootloader_flash_config_esp32s3.c.obj	9cbdbec186d05256
1016	1383	7762769674438428	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp32s3/bootloader_soc.c.obj	609ade9bdf40be2a
1002	1397	7762769674478349	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp32s3/bootloader_sha.c.obj	8fd96d1972af5e55
1045	1426	7762769674778671	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_panic.c.obj	a2f577d57312d660
976	1438	7762769674958288	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_console.c.obj	7834acfe5e151b68
1111	1498	7762769675548375	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/esp32s3/esp_efuse_table.c.obj	27af08671053807b
1136	1523	7762769675778266	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/esp32s3/esp_efuse_rtc_calib.c.obj	380a10bac318eb69
734	1555	7762769675688370	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/bootloader_flash/src/bootloader_flash.c.obj	926ccc168973f291
951	1589	7762769676368293	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_init.c.obj	c6355b12f3fb7cbd
1124	1606	7762769676608385	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/esp32s3/esp_efuse_fields.c.obj	cfc0e5b060c99f6b
1031	1686	7762769677408412	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp32s3/bootloader_esp32s3.c.obj	d7d6f842abf8a923
1308	1715	7762769677788311	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/src/esp_efuse_fields.c.obj	356a62fa95d59977
1255	1751	7762769678073705	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/esp32s3/esp_efuse_utility.c.obj	5f4e985a338d28a4
1383	1769	7762769678233740	esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/esp_err.c.obj	99c794d556d39858
850	1781	7762769678233740	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_utility.c.obj	1bfc4662f64c8717
1439	1819	7762769678833746	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/esp_memory_utils.c.obj	cbbc98bf571c16db
1498	1836	7762769678985991	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32s3/cpu_region_protect.c.obj	935954db63fccc5e
1426	1848	7762769678803784	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32s3/esp_cpu_intr.c.obj	fb094241e8055404
1297	1860	7762769679165961	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/src/esp_efuse_api.c.obj	68d79b9069a43a1b
1398	1880	7762769679185983	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/cpu.c.obj	d8b7cb77cd66a7a7
939	1895	7762769679466136	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp_image_format.c.obj	fc4d2a8959688f
373	1939	7762769680015948	esp-idf/micro-ecc/CMakeFiles/__idf_micro-ecc.dir/uECC_verify_antifault.c.obj	ebbcf0ae030c0961
1555	2001	7762769680646128	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32s3/rtc_clk_init.c.obj	36402f1873443210
1356	2013	7762769680586210	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/src/efuse_controller/keys/with_key_purposes/esp_efuse_api_key.c.obj	ff72c83380b3384
1344	2028	7762769680876096	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/src/esp_efuse_utility.c.obj	8841c2f8bd051cfa
1769	2052	7762769681095502	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_crc.c.obj	c75e69e1b5056044
1782	2067	7762769681295768	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_sys.c.obj	d36d1b391ae30994
1715	2093	7762769681585992	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32s3/chip_info.c.obj	47a4013344eefe05
1860	2103	7762769681675676	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_longjmp.S.obj	75c732a741ae3431
1848	2130	7762769681895679	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_efuse.c.obj	bb43ec6928a34d30
1606	2167	7762769682276989	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32s3/rtc_sleep.c.obj	ff7d956b062b132f
1752	2174	7762769682376775	esp-idf/esp_common/CMakeFiles/__idf_esp_common.dir/src/esp_err_to_name.c.obj	77ef1f783bf15730
1819	2182	7762769682456759	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_uart.c.obj	f89b75a6e52df0f2
1881	2186	7762769682516764	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_systimer.c.obj	104cb19e39513633
1836	2219	7762769682862191	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_spiflash.c.obj	d88ac0c4f0dc1c28
1523	2228	7762769682942256	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32s3/rtc_clk.c.obj	b13fc56d667b9128
1895	2229	7762769682962378	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_wdt.c.obj	a7982426c8675e41
2001	2231	7762769682992213	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_cache_writeback_esp32s3.S.obj	658e916694c22b5f
1589	2243	7762769683112162	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32s3/rtc_init.c.obj	bcc879320507bfa3
1686	2253	7762769683209083	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32s3/rtc_time.c.obj	dd06ffbb7f3757ce
1940	2272	7762769683409012	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_cache_esp32s2_esp32s3.c.obj	a57b30e00182edb2
2052	2310	7762769683794541	esp-idf/log/CMakeFiles/__idf_log.dir/log_noos.c.obj	d61c38d75d002914
2067	2319	7762769683884299	esp-idf/main/CMakeFiles/__idf_main.dir/bootloader_start.c.obj	f7a542af7d9de78f
2028	2356	7762769684274213	esp-idf/log/CMakeFiles/__idf_log.dir/log_buffers.c.obj	662891cc89f4c633
2013	2362	7762769684334289	esp-idf/log/CMakeFiles/__idf_log.dir/log.c.obj	11cc5102e3b987b6
2362	2498	7762769685704432	esp-idf/log/liblog.a	a17a559161c59143
2498	2650	7762769687214862	esp-idf/esp_rom/libesp_rom.a	7b803647f408fc23
2650	2782	7762769688542912	esp-idf/esp_common/libesp_common.a	72c724c510af62be
2782	2937	7762769690094339	esp-idf/esp_hw_support/libesp_hw_support.a	f4250f49266e5a08
2937	3067	7762769691383903	esp-idf/esp_system/libesp_system.a	d09bf9bee9ff473b
3067	3218	7762769692903826	esp-idf/efuse/libefuse.a	10a352188240336f
3218	3407	7762769694797081	esp-idf/bootloader_support/libbootloader_support.a	2878245af2b888bc
3408	3538	7762769696097221	esp-idf/esp_bootloader_format/libesp_bootloader_format.a	71bcbb34542bf20c
3538	3671	7762769697437055	esp-idf/spi_flash/libspi_flash.a	7f8bf412a5b41d6
3671	3816	7762769698877229	esp-idf/hal/libhal.a	fe38390d77d11231
3816	3950	7762769700217264	esp-idf/micro-ecc/libmicro-ecc.a	8ee557e9e8037178
3950	4137	7762769702095389	esp-idf/soc/libsoc.a	9bfd60c25dcbe31c
4137	4272	7762769703446616	esp-idf/xtensa/libxtensa.a	8c0cf69e4cb36b70
4272	4401	7762769704731596	esp-idf/main/libmain.a	1d694bae0c529d9d
4401	4659	7762769707261593	bootloader.elf	28facbdb2f0f20e1
4659	5003	7762769710760701	.bin_timestamp	9c461d6e1be7c711
4659	5003	7762769710760701	E:/APPprj/ESP32-S3-Touch-LCD-1.85C-Test/build/bootloader/.bin_timestamp	9c461d6e1be7c711
5004	5094	0	esp-idf/esptool_py/CMakeFiles/bootloader_check_size	712e09a6a7a06c95
5004	5094	0	E:/APPprj/ESP32-S3-Touch-LCD-1.85C-Test/build/bootloader/esp-idf/esptool_py/CMakeFiles/bootloader_check_size	712e09a6a7a06c95
15	107	0	esp-idf/esptool_py/CMakeFiles/bootloader_check_size	712e09a6a7a06c95
15	107	0	E:/APPprj/ESP32-S3-Touch-LCD-1.85C-Test/build/bootloader/esp-idf/esptool_py/CMakeFiles/bootloader_check_size	712e09a6a7a06c95
16	117	0	esp-idf/esptool_py/CMakeFiles/bootloader_check_size	712e09a6a7a06c95
16	117	0	E:/APPprj/ESP32-S3-Touch-LCD-1.85C-Test/build/bootloader/esp-idf/esptool_py/CMakeFiles/bootloader_check_size	712e09a6a7a06c95
15	106	0	esp-idf/esptool_py/CMakeFiles/bootloader_check_size	712e09a6a7a06c95
15	106	0	E:/APPprj/ESP32-S3-Touch-LCD-1.85C-Test/build/bootloader/esp-idf/esptool_py/CMakeFiles/bootloader_check_size	712e09a6a7a06c95
14	105	0	esp-idf/esptool_py/CMakeFiles/bootloader_check_size	712e09a6a7a06c95
14	105	0	E:/APPprj/ESP32-S3-Touch-LCD-1.85C-Test/build/bootloader/esp-idf/esptool_py/CMakeFiles/bootloader_check_size	712e09a6a7a06c95
