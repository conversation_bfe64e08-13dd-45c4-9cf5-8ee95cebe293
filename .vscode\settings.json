{"idf.openOcdConfigs": ["board/esp32s3-builtin.cfg"], "idf.portWin": "COM10", "idf.customExtraVars": {"OPENOCD_SCRIPTS": "D:\\Espressif\\tools\\openocd-esp32\\v0.12.0-esp32-20240318/openocd-esp32/share/openocd/scripts", "IDF_CCACHE_ENABLE": "1", "ESP_ROM_ELF_DIR": "D:\\Espressif\\tools\\esp-rom-elfs\\20230320/", "IDF_TARGET": "esp32s3"}, "clangd.path": "D:\\Espressif\\tools\\esp-clang\\16.0.1-fe4f10a809\\esp-clang\\bin\\clangd.exe", "clangd.arguments": ["--background-index", "--query-driver=D:\\Espressif\\tools\\xtensa-esp-elf\\esp-13.2.0_20240530\\xtensa-esp-elf\\bin\\xtensa-esp32s3-elf-gcc.exe", "--compile-commands-dir=e:\\APPprj\\ESP32-S3-Touch-LCD-1.85C-Test\\build"], "idf.flashType": "UART"}